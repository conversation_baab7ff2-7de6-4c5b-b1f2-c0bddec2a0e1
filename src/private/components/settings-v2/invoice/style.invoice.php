<style>
    .invoice-manager{
        display: grid;
        grid-template-columns: 1fr 780px;
        gap: 30px;
    }

    .invoice-preview iframe{
        position: sticky;
        top: 75px;
        width: 100%;
        height: min(90vh, 1047px);
        overflow: scroll;
    }

    @media (max-width: 1500px) {
        .invoice-manager {
            grid-template-columns: 1fr;
        }

        #manageInvoice{
            width: 100%;
            max-width: 100%;
        }

        .invoice-preview iframe{
            width: 100%;
        }
    }

    /* Sections */
    #manageInvoice h4 {
        margin-bottom: 15px;
        font-size: 1.2rem;
        color: #333;
        border-bottom: 2px solid #eee;
        padding-bottom: 5px;
    }

    /* Labels */
    .form-label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #444;
    }

    /* Form Controls */
    input[type="range"] {
        width: 100%;
    }

    .form-control,
    .form-range,
    select {
        display: block;
        width: 100%;
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid #ccc;
        font-size: 0.95rem;
        background-color: #fff;
    }

    /* Textarea */
    textarea.form-control {
        resize: vertical;
        min-height: 80px;
    }

    /* Checkboxes */
    .form-check {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
    }

    .form-check-input {
        width: 18px;
        height: 18px;
        accent-color: #007bff;
    }

    /* Custom Layout Helpers */
    .form-row,
    .form-row-x,
    .form-outline,
    .form-outline-x,
    .form-group {
        margin-bottom: 15px;
    }

    .select {
        appearance: none;
        background: url("data:image/svg+xml,%3Csvg fill='gray' height='20' viewBox='0 0 24 24' width='20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E") no-repeat right 10px center;
        background-size: 16px;
    }

    .form-material.floating label {
        display: block;
        font-size: 0.85rem;
        margin-top: 5px;
        color: #777;
    }

    #manageInvoice::-webkit-scrollbar {
        width: 6px;
    }
    #manageInvoice::-webkit-scrollbar-thumb {
        background-color: #bbb;
        border-radius: 4px;
    }

    /* SLIDER Stlyes */
    .slider{
        position: relative;
        width: 100%;
    }

    input[type="range"] {
        -webkit-appearance: none;
        width: 100%;
        height: 3px;
        background: linear-gradient(
            to right,
            color-mix(in srgb, transparent 80%, var(--primary)) 0%,
            color-mix(in srgb, transparent 50%, var(--primary)) 3%,
            var(--primary) 7%,
            var(--primary) 93%,
            color-mix(in srgb, transparent 50%, var(--primary)) 97%,
            color-mix(in srgb, transparent 80%, var(--primary)) 100%
        );
        position: relative;
    }

    /* Circle thumb */
    input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        height: 40px;
        width: 60px;
        background: var(--mainbackground);
        border: 1px solid var(--primary);
        border-radius: 5%;
        position: relative;
        z-index: 2;
        cursor: pointer;
    }

    .range-bubble {
        position: absolute;
        top: 2px;
        width: 50px;
        height: 30px;
        background: var(--mainbackground);
        color: var(--primary);
        font-weight: bold;
        text-align: center;
        line-height: 30px;
        pointer-events: none;
        z-index: 3;
        font-size: 14px;
    }

    /* GRID SYSTEM */
    .cs-item,
    .fs-item,
    .hfs-item,
    .colors-item,
    .ip-item{
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        grid-gap: 20px;
        align-items: center;
    }

    .cs-item{
        grid-template-columns: 300px 200px 1fr;
    }

    .colors-item{
        grid-gap: 30px;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
    }

    .colors-item .form-row{
        display: grid;
        grid-template-columns: auto 40px;
        align-items: center;
        gap: 10px;
    }

    .colors-item .form-row label{
        margin-bottom: 0;
        padding-left: 10px;
    }

    .colors-item .form-row input[type="color"]{
        height: 100%;
        padding: 2px;
        height: 40px;
        border: none;
        padding: 0;
        background: none;
        cursor: pointer;
        appearance: none;
        -webkit-appearance: none;
        border-radius: 0;
    }

    .form-outline,
    .form-row{
        margin-bottom: 0;
    }

    /* Custom Disclosure Upload Section */
    .disclosure-upload-container div{
        color: var(--primary);
    }

    .disclosure-upload-container .fa-file-import{
        transform: translateY(2px);
        color: var(--primary);
    }

    .save-action{
        text-align: center;
    }

    /* Custom Section Sizes */
    .disclosure-section .disclosure-upload-container{
        max-width: 700px;
    }

    .qr-section .form-outline{
        max-width: 500px;
    }
    

    /* FONT Styles */
    .courier{
        font-family: "Courier New", Courier, monospace;
    }
    .courierB{
        font-family: "Courier New", Courier, monospace;
        font-weight: bold;
    }
    .courierBI{
        font-family: "Courier New", Courier, monospace;
        font-weight: bold;
        font-style: italic;
    }
    .courierI{
        font-family: "Courier New", Courier, monospace;
        font-style: italic;
    }
    .helvetica{
        font-family: Helvetica, Arial, sans-serif;
    }
    .helveticaB{
        font-family: Helvetica, Arial, sans-serif;
        font-weight: bold;
    }
    .helveticaBI{
        font-family: Helvetica, Arial, sans-serif;
        font-weight: bold;
        font-style: italic;
    }
    .helveticaI{
        font-family: Helvetica, Arial, sans-serif;
        font-style: italic;
    }
    .timesB{
        font-family: "Times New Roman", Times, serif;
        font-weight: bold;
    }
    .timesBI{
        font-family: "Times New Roman", Times, serif;
        font-weight: bold;
        font-style: italic;
    }
    .timesI{
        font-family: "Times New Roman", Times, serif;
        font-style: italic;
    }
</style>