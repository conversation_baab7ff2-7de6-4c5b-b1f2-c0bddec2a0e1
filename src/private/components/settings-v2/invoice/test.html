<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Custom MDB Select (Pure CSS)</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      padding: 2rem;
      background: #f4f4f4;
    }

    .custom-select-wrapper {
      position: relative;
      width: 250px;
    }

    .custom-select {
      background: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      cursor: pointer;
      padding: 12px;
      position: relative;
      transition: all 0.3s ease;
    }

    .custom-select::after {
      content: '▾';
      position: absolute;
      right: 10px;
      top: 14px;
      pointer-events: none;
    }

    input[type="checkbox"] {
      display: none;
    }

    .options {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid #ccc;
      border-top: none;
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
      z-index: 5;
    }

    input[type="checkbox"]:checked ~ .options {
      max-height: 200px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .option {
      padding: 12px;
      cursor: pointer;
      transition: background 0.2s;
    }

    .option:hover {
      background: #f1f1f1;
    }

    /* Floating label effect */
    .label {
      position: absolute;
      top: -8px;
      left: 12px;
      background: white;
      padding: 0 4px;
      font-size: 0.75em;
      color: #666;
    }
  </style>
</head>
<body>

  <div class="custom-select-wrapper">
    <label class="label">Choose an option</label>
    <input type="checkbox" id="toggle" />
    <label for="toggle" class="custom-select">Select an option</label>
    
    <div class="options">
      <div class="option">Option 1</div>
      <div class="option">Option 2</div>
      <div class="option">Option 3</div>
    </div>
  </div>

</body>
</html>
