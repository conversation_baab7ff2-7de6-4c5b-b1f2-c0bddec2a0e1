<?php
$component = "settings-v2";
$sub_component = "Printing Preferences";
$shopid = $_COOKIE['shopid'];

include getRulesComponent($component);
include getHeadGlobal($component);
include COMPONENTS_PRIVATE_PATH . "/settings-v2/invoice/style.invoice.php";
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);

require_once COMPONENTS_PRIVATE_PATH . "/invoice-v2/invoice.php";
include COMPONENTS_PRIVATE_PATH . "/settings-v2/invoice/rules.invoice.php";
?>

<link rel="stylesheet" href="https://staging.shopbosspro.com/src/public/MDB5v6.4/plugins/css/all.min.css" />


<main id="settings" class="min-vh-100">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/settings/settings.php"
                           class="text-secondary">Settings</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">Invoice</h2>
                    </div>
                    <hr/>
                </div>
            </div>
        </div>
    </div>

    <div class="invoice-manager">
        <div id="manageInvoice">
            <div class="custom-ivnoice-status-section mb-5">
                <h4>Custom Invoice Status</h4>
                
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="defaultPrinting" name="defaultPrinting" <?= ($invoice['default_printing'] ?? true) ? 'checked' : '' ?>>
                    <label class="form-check-label" for="defaultPrinting">Use Custom Inoice</label>
                </div>
            </div>
            <form action="" method="put" id="invoiceForm">
                <div class="company-section mb-5">
                    <h4>Company Logo</h4>
                    
                    <div class="cs-item">
                        <div class="form-row">
                            <select class="select" name="companyPlacement" id="companyPlacement">
                                <option value="center" <?= ($invoice['company_logo_align'] ?? 'center') === 'center' ? 'selected' : '' ?>>Center</option>
                                <option value="left" <?= ($invoice['company_logo_align'] ?? 'center') === 'left' ? 'selected' : '' ?>>Left</option>
                                <option value="right" <?= ($invoice['company_logo_align'] ?? 'center') === 'right' ? 'selected' : '' ?>>Right</option>
                            </select>
                            <label for="companyPlacement" class="form-label select-label">Align Logo</label>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showCompanyLogo" name="showCompanyLogo" <?= ($invoice['show_company_logo'] ?? true) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="showCompanyLogo">Show Logo</label>
                        </div>

                        <div class="form-row range-control">
                            <label for="logoSize" class="form-label">Logo Size</label>
                            <div class="slider">
                                <?php 
                                $logoSize = str_replace('px', '', $invoice['company_logo_size'] ?? '90');
                                $logoAdjustment = $logoSize - 90;
                                ?>
                                <input type="range" id="logoSize" name="logoSize" min="-40" max="40" value="<?= $logoAdjustment ?>" oninput="updateSliderBubble(this, 90)">
                                <div class="range-bubble"><?= $logoSize ?>px</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="font-section mb-5">
                    <h4>Font Settings</h4>

                    <div class="fs-item">
                        <div class="form-outline">
                            <select class="" name="fontStyle" id="fontStyle">
                                <option class="courier" value="courier" <?= ($invoice['fontStyle'] ?? 'courier') === 'courier' ? 'selected' : '' ?>>Courier</option>
                                <option class="courierB" value="courierB" <?= ($invoice['fontStyle'] ?? 'courier') === 'courierB' ? 'selected' : '' ?>>Courier Bold</option>
                                <option class="courierBI" value="courierBI" <?= ($invoice['fontStyle'] ?? 'courier') === 'courierBI' ? 'selected' : '' ?>>Courier Bold Italic</option>
                                <option class="courierI" value="courierI" <?= ($invoice['fontStyle'] ?? 'courier') === 'courierI' ? 'selected' : '' ?>>Courier Italic</option>
                                <option class="helvetica" value="helvetica" <?= ($invoice['fontStyle'] ?? 'courier') === 'helvetica' ? 'selected' : '' ?>>Helvetica</option>
                                <option class="helveticaB" value="helveticaB" <?= ($invoice['fontStyle'] ?? 'courier') === 'helveticaB' ? 'selected' : '' ?>>Helvetica Bold</option>
                                <option class="helveticaBI" value="helveticaBI" <?= ($invoice['fontStyle'] ?? 'courier') === 'helveticaBI' ? 'selected' : '' ?>>Helvetica Bold Italic</option>
                                <option class="helveticaI" value="helveticaI" <?= ($invoice['fontStyle'] ?? 'courier') === 'helveticaI' ? 'selected' : '' ?>>Helvetica Italic</option>
                                <option class="timesB" value="timesB" <?= ($invoice['fontStyle'] ?? 'courier') === 'timesB' ? 'selected' : '' ?>>Times Bold</option>
                                <option class="timesBI" value="timesBI" <?= ($invoice['fontStyle'] ?? 'courier') === 'timesBI' ? 'selected' : '' ?>>Times Bold Italic</option>
                                <option class="timesI" value="timesI" <?= ($invoice['fontStyle'] ?? 'courier') === 'timesI' ? 'selected' : '' ?>>Times Italic</option>
                            </select>
                            <label for="fontStyle" class="form-label select-label">Choose Font Familly</label>
                        </div>

                        <div class="form-row">
                            <label for="headerFontSize" class="form-label">Header Font Size</label>
                            <div class="slider">
                                <input type="range" id="headerFontSize" name="headerFontSize" min="-5" max="5" step="1" 
                                    value="<?= getFontAdjustment($invoice['font_header'] ?? null, 11) ?>" 
                                    oninput="updateSliderBubble(this, 11)">
                                <div class="range-bubble"><?= $invoice['font_header'] ?? '11px' ?></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <label for="invoiceTitleFontSize" class="form-label">Invoice Title Font Size</label>
                            <div class="slider">
                                <input type="range" id="invoiceTitleFontSize" name="invoiceTitleFontSize" min="-5" max="5" step="1" 
                                    value="<?= getFontAdjustment($invoice['font_title'] ?? null, 18) ?>" 
                                    oninput="updateSliderBubble(this, 18)">
                                <div class="range-bubble"><?= $invoice['font_title'] ?? '18px' ?></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <label for="contentFontSize" class="form-label">Body Font Size</label>
                            <div class="slider">
                                <input type="range" id="contentFontSize" name="contentFontSize" min="-5" max="5" step="1" 
                                    value="<?= getFontAdjustment($invoice['font_body'] ?? null, 10) ?>" 
                                    oninput="updateSliderBubble(this, 10)">
                                <div class="range-bubble"><?= $invoice['font_body'] ?? '10px' ?></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <label for="footerFontSize" class="form-label">Total Box Font Size</label>
                            <div class="slider">
                                <input type="range" id="footerFontSize" name="footerFontSize" min="-5" max="5" step="1" 
                                    value="<?= getFontAdjustment($invoice['font_footer'] ?? null, 9) ?>" 
                                    oninput="updateSliderBubble(this, 9)">
                                <div class="range-bubble"><?= $invoice['font_footer'] ?? '9px' ?></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <label for="disclosureFontSize" class="form-label">Disclosure Font Size</label>
                            <div class="slider">
                                <input type="range" id="disclosureFontSize" name="disclosureFontSize" min="-5" max="5" step="1" 
                                    value="<?= getFontAdjustment($invoice['font_disclosure'] ?? null, 9) ?>" 
                                    oninput="updateSliderBubble(this, 9)">
                                <div class="range-bubble"><?= $invoice['font_disclosure'] ?? '9px' ?></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="disclosure-section mb-5">
                    <h4>Disclosure Settings</h4>

                    <div class="ds-item">
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="customDisclosurePage" name="customDisclosurePage" <?= ($invoice['new_page_for_disclosure'] ?? false) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="customDisclosurePage">New Page For Custom Disclosure</label>
                        </div>

                        <div class="form-group mb-4" id="disclosureBox">
                            <?php
                            $disclosurePath = PDFINVOICES_PATH . "/custom-disclosure/" . $shopid . '/disclosure.pdf';
                            $disclosureExists = file_exists($disclosurePath);
                            $disclosureSize = $disclosureExists ? filesize($disclosurePath) : 0;
                            $disclosureDate = $disclosureExists ? date('M j, Y g:i A', filemtime($disclosurePath)) : '';
                            ?>

                            <div class="disclosure-upload-container">
                                <input type="file" name="disclosurePDF" id="disclosurePDF" accept="application/pdf" style="display: none;">

                                <?php if ($disclosureExists): ?>
                                    <div class="disclosure-status mb-3 p-3 border rounded">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div>
                                                <i class="fa-solid fa-file-pdf text-danger me-2"></i>
                                                <strong>Disclosure PDF Uploaded</strong>
                                                <div class="small">
                                                    Size: <?= number_format($disclosureSize / 1024, 1) ?> KB |
                                                    Uploaded: <?= $disclosureDate ?>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-secondary btn-sm" onclick="document.getElementById('disclosurePDF').click();">
                                                <i class="fa-light fa-file-import me-2"></i>Replace
                                            </button>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="disclosure-upload-prompt p-3 border border-dashed rounded text-center">
                                        <i class="fa-light fa-cloud-upload fa-2x text-muted mb-2"></i>
                                        <div class="mb-2">No disclosure PDF uploaded</div>
                                        <button type="button" class="btn btn-primary" onclick="document.getElementById('disclosurePDF').click();">
                                            <i class="fa-light fa-file-import me-2"></i>Upload Disclosure PDF
                                        </button>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div id="basicDisclosures">
                            <div class="form-outline mb-4">
                                <textarea class="form-control" id="signatureDisclosure" name="signatureDisclosure" rows="6" maxlength="1962" ai-writing-tool><?= htmlspecialchars($invoice['signature_disclosure'] ?? $roDisclosure) ?></textarea>
                                <label class="form-label">Signature Disclosure</label>
                            </div>

                            <div class="form-outline">
                                <textarea class="form-control" id="warrantyDisclosure" name="warrantyDisclosure" rows="6" maxlength="1962" ai-writing-tool><?= htmlspecialchars($invoice['warranty_disclosure'] ?? $roDisclosure) ?></textarea>
                                <label class="form-label">Warranty Disclosure</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="header-footer-section mb-5">
                    <h4>Header | Footer Space</h4>

                    <div class="hfs-item">
                        <div class="form-outline-x">
                            <label for="headerSpace" class="form-label">Header Space</label>
                            <div class="slider">
                                <input type="range" id="headerSpace" name="headerSpace" min="-100" max="100" step="10" 
                                    value="<?= $invoice['header_space'] ?? 0 ?>" 
                                    oninput="updateSliderBubble(this, 180)">
                                <div class="range-bubble"><?= ($invoice['header_space'] ?? 0) + 180 ?>px</div>
                            </div>
                        </div>

                        <div class="form-outline-x">
                            <label for="footerSpace" class="form-label">Footer Space</label>
                            <div class="slider">
                                <input type="range" id="footerSpace" name="footerSpace" min="-100" max="100" step="10" 
                                    value="<?= $invoice['footer_space'] ?? 0 ?>" 
                                    oninput="updateSliderBubble(this, 160)">
                                <div class="range-bubble"><?= ($invoice['footer_space'] ?? 0) + 160 ?>px</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="color-section mb-5">
                    <h4>Color Settings</h4>

                    <div class="colors-item">
                        <div class="form-row border rounded">
                            <label for="contentColor" class="form-label">Body Color</label>
                            <input type="color" class="form-control" id="contentColor" name="contentColor" value="<?= $invoice['color_body'] ?? '#262626' ?>">
                        </div>

                        <div class="form-row border rounded">
                            <label for="invoiceTitleColor" class="form-label">Invoice Title Color</label>
                            <input type="color" class="form-control" id="invoiceTitleColor" name="invoiceTitleColor" value="<?= $invoice['color_title'] ?? '#262626' ?>">
                        </div>

                        <div class="form-row border rounded">
                            <label for="tableHeaderColor" class="form-label">Vehicle Issue Color</label>
                            <input type="color" class="form-control" id="tableHeaderColor" name="tableHeaderColor" value="<?= $invoice['color_issue_text'] ?? '#ffffff' ?>">
                        </div>

                        <div class="form-row border rounded">
                            <label for="tableHeaderBackground" class="form-label">Vehicle Issue Background</label>
                            <input type="color" class="form-control" id="tableHeaderBackground" name="tableHeaderBackground" value="<?= $invoice['color_issue_bg'] ?? '#262626' ?>">
                        </div>
                    </div>
                </div>

                <div class="qr-section mb-5">
                    <h4>QR Settings</h4>

                    <div class="form-outline">
                        <input type="text" name="qr_text" id="qr_text" class="form-control" value="<?= htmlspecialchars($invoice['qr'] ?? '') ?>" maxlength="1962">
                        <label for="qr_text" class="form-label">QR Text</label>
                    </div>
                </div>

                <div class="whatermark-section mb-5">
                    <h4>Watermark Settings</h4>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="watermark" name="watermark" <?= ($invoice['whatermark'] ?? false) ? 'checked' : '' ?>>
                        <label class="form-check-label" for="watermark">Show Watermark</label>
                    </div>
                </div>
            </form>

            <div class="invoice-preferences">
                <h4>Invoice Preferences</h4>

                <div class="ip-item" id="invoicePreferencesItems">
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" id="printtechstory" name="printtechstory" class="form-check-input" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['print_tech_story']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label class="form-check-label" for="printtechstory">
                            Print Tech Story on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays technician notes added to a customer concern in a repair order."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="printvitotals" name="printvitotals" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['print_vi_totals']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="printvitotals" class="form-check-label">
                            Show Subtotals for each Concern
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Shows a dollar amount of each concern along with the total of the repair order. "></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="printadvisorcomments" name="printadvisorcomments" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['print_advisor_comments']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="printadvisorcomments" class="form-check-label">
                            Print Advisor Comments on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays advisor notes added to a customer concern in a repair order."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="replacerowithtag" name="replacerowithtag" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['replace_ro_with_tag']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label class="form-check-label">
                            Print Tag Number in place of RO Number
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Display input Service Dispatch Tag number in place of the Repair Order Number."></i>
                        </label>
                    </div>
                    <?php if ($company['matco'] == 'yes'){ ?>
                        <div class="form-check form-switch mb-4 custom-label">
                            <input type="checkbox" class="form-check-input" id="printscanresults" name="printscanresults" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'SettingsController@update')"
                                <?= strtolower($settings['print_scan_result']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                            <label for="printscanresults" class="form-check-label">
                                Print Scan Tool Results on Invoice
                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays Scan Tool results on printed invoice."></i>
                            </label>
                        </div>
                    <?php } ?>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showtechoninvoice" name="showtechoninvoice" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_tech_on_invoice']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showtechoninvoice" class="form-check-label">
                            Show Tech on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays the Technicians name that performed the labor."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showtirepressure" name="showtirepressure" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_tire_pressure']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showtirepressure" class="form-check-label">
                            Show Tire Pressure & Tread Depth on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays 4-wheel vehicle diagram at the top of the invoice to indicate tread depth and pressure. Will be displayed on all invoices."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showlaborhoursonro" name="showlaborhoursonro" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_labor_hours_on_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showlaborhoursonro" class="form-check-label">
                            Show Labor Hours on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays hours billed per labor line."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="printbar" name="printbar" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['print_bar']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="printbar" class="form-check-label">
                            Print State/Local License # on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays Company State/Local License number input in the Company settings."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="itemizefeesprintedro" name="itemizefeesprintedro" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['itemize_fees_printed_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="itemizefeesprintedro" class="form-check-label">
                            Itemize Fees on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="A breakdown of any fees associated with the repair."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showdeclined" name="showdeclined" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_declined']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showdeclined" class="form-check-label">
                            Show Declined Items on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Declined repairs will be shown on the invoice as declined by the customer."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showpayments" name="showpayments" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_payments']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showpayments" class="form-check-label">
                            Show Payments on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Indicates any payments made on the repair order."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="nexpartusername" name="nexpartusername" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['next_part_user_name']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="nexpartusername" class="form-check-label">
                            Show Line Item Prices
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays individual Part and Labor line prices in addition to the total of the concern."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="printpayments" name="printpayments" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['print_payments']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="printpayments" class="form-check-label">
                            Itemize Payments on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Gives a breakdown of payments made on repair order."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showadvisoroninvoice" name="showadvisoroninvoice" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_advisor_on_invoice']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showadvisoroninvoice" class="form-check-label">
                            Show Service Writer on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Shows who the service writer was assigned to the repair order."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="partsdiscountonro" name="partsdiscountonro" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['parts_discount_on_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="partsdiscountonro" class="form-check-label">
                            Show Parts Discount on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Makes the parts discount savings visible to the customer on the invoice."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showinvoicenumber" name="showinvoicenumber" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_invoice_number']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showinvoicenumber" class="form-check-label">
                            Show Invoice Number on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays the invoice number associated with the repair order."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showpartnumberonprintedro" name="showpartnumberonprintedro" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_part_number_on_printed_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showpartnumberonprintedro" class="form-check-label">
                            Show Part Numbers on RO & Quote Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Part Numbers will be displayed on Repair order and Quote invoices in addition to the part description."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showpartnumberonprintedps" name="showpartnumberonprintedps" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_part_number_on_printed_ps']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showpartnumberonprintedps" class="form-check-label">
                            Show Part Numbers on PS Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Part Numbers will be displayed for over-the-counter part sale invoices in addition to the part description."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="customropage" name="customropage" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_tech_hours']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label class="form-check-label">
                            Show Labor Hours on Tech Worksheet
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technicians will see how many hours were assigned per labor line on printed worksheet."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showcustphonewo" name="showcustphonewo" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_cust_phone_wo']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label class="form-check-label">
                            Show Customer Phone on Tech Worksheet
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technicians will see the customer's phone number on printed worksheets."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showsourceonprintedro" name="showsourceonprintedro" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_source_on_printed_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showsourceonprintedro" class="form-check-label">
                            Show Source on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays notated marketing source on the invoice. This is notated in the source field on a repair order. Used to track Marketing ROI."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="printcommlog" name="printcommlog" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['print_comm_log']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="printcommlog" class="form-check-label">
                            Print Communication Log on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays text and email communication you have had with the customer on the invoice for the customer to see."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showrevapps" name="showrevapps" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_rev_apps']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showrevapps" class="form-check-label">
                            Display Revision Approvals on Invoices
                            <i class="fa fa-circle-info" title="For California shops to meet the BAR requirements stated here https://www.bar.ca.gov/pdf/writeitright.pdf" data-mdb-toggle="tooltip" data-mdb-html="true"></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4 custom-label">
                        <input type="checkbox" class="form-check-input" id="showpcodeoninvoice" name="showpcodeoninvoice" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'CompanyController@update')"
                            <?= strtolower($company['show_pcode_on_invoice']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showpcodeoninvoice" class="form-check-label">
                            Show Part Code on Invoice
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays part codes based on what is assigned to each part. Ex. New, Used, Re-Manufactured."></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4">
                        <input type="checkbox" class="form-check-input" id="signedinvoicesonstmts" name="signedinvoicesonstmts" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'SettingsController@update')"
                            <?= strtolower($settings['signed_invoice_son_stmts']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="signedinvoicesonstmts" class="form-check-label">
                            Display Signed Invoices on Statements
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-html="true" data-mdb-placement="right" title="With this on, it will pull most recent signed invoice"></i>
                        </label>
                    </div>
                    <div class="form-check form-switch mb-4">
                        <input type="checkbox" class="form-check-input" id="showpromisedateoninvoice" name="showpromisedateoninvoice" onchange="savePreference(this.name, this.checked ? 'yes' : 'no', 'SettingsController@update')"
                            <?= strtolower($settings['show_promise_date_on_invoice']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                        <label for="showpromisedateoninvoice" class="form-check-label">
                            Show Promise Date on Invoice
                            <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-html="true" data-mdb-placement="right" title="With this on, it will it will display promise date on invoice"></i>
                        </label>
                    </div>
                    <div class="form-outline mb-4 custom-label">
                        <input class="form-control" tabindex="6" type="text" id="invoicetitle" name="invoicetitle" onchange="savePreference(this.name, this.value, 'CompanyController@update')"
                            value="<?= $company['invoice_title']; ?>">
                        <label for="invoicetitle" class="form-label">
                            Invoice Label
                        </label>
                    </div>
                    <div class="form-outline mb-4 custom-label">
                        <input class="form-control" tabindex="6" type="text" id="estimatetitle" name="estimatetitle" onchange="savePreference(this.name, this.value, 'CompanyController@update')"
                            value="<?= $company['estimate_title']; ?>">
                        <label id="estimatetitle" class="form-label">
                            Estimate Label
                        </label>
                    </div>
                    <div class="form-outline mb-4 custom-label">
                        <input class="form-control" tabindex="6" type="text" id="milesinlabel" name="milesinlabel" onchange="savePreference(this.name, this.value, 'CompanyController@update')"
                            value="<?= $company['miles_in_label']; ?>">
                        <label for="milesinlabel" class="form-label">
                            Miles In Label
                        </label>
                    </div>
                    <div class="form-outline mb-4 custom-label">
                        <input class="form-control" tabindex="6" type="text" id="milesoutlabel" name="milesoutlabel" onchange="savePreference(this.name, this.value, 'CompanyController@update')"
                            value="<?= $company['miles_out_label']; ?>">
                        <label for="milesoutlabel" class="form-label">
                            Miles Out Label
                        </label>
                    </div>
                    <div class="form-outline mb-4 custom-label">
                        <input class="form-control" tabindex="6" type="text" id="quotelabel" name="quotelabel" onchange="savePreference(this.name, this.value, 'CompanyController@update')"
                            value="<?= $company['quote_label']; ?>">
                        <label for="quotelabel" class="form-label">
                            Quote Label
                        </label>
                    </div>
                </div>
            </div>

            <div class="save-action">
                <button class="btn btn-primary btn-md" onclick="saveInvoice()" type="button">Save</button>
            </div>
        </div>
        <div class="invoice-preview">
            <iframe id="invoiceFrame" src="https://staging.shopbosspro.com/src/private/components/invoice-v2/print-invoice.php" frameborder="0"></iframe>
        </div>
    </div>
</main>

<?php
$component = '';
include(AI_WRITING_TOOL);
include getScriptsGlobal('');
include COMPONENTS_PRIVATE_PATH . "/settings-v2/invoice/scripts.invoice.php";
// include getFooterComponent($component);
?>